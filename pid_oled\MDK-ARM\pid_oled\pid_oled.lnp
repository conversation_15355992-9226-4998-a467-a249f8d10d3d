--cpu Cortex-M3
"pid_oled\startup_stm32f103xb.o"
"pid_oled\main.o"
"pid_oled\gpio.o"
"pid_oled\i2c.o"
"pid_oled\stm32f1xx_it.o"
"pid_oled\stm32f1xx_hal_msp.o"
"pid_oled\stm32f1xx_hal_gpio_ex.o"
"pid_oled\stm32f1xx_hal_i2c.o"
"pid_oled\stm32f1xx_hal.o"
"pid_oled\stm32f1xx_hal_rcc.o"
"pid_oled\stm32f1xx_hal_rcc_ex.o"
"pid_oled\stm32f1xx_hal_gpio.o"
"pid_oled\stm32f1xx_hal_dma.o"
"pid_oled\stm32f1xx_hal_cortex.o"
"pid_oled\stm32f1xx_hal_pwr.o"
"pid_oled\stm32f1xx_hal_flash.o"
"pid_oled\stm32f1xx_hal_flash_ex.o"
"pid_oled\stm32f1xx_hal_exti.o"
"pid_oled\stm32f1xx_hal_tim.o"
"pid_oled\stm32f1xx_hal_tim_ex.o"
"pid_oled\system_stm32f1xx.o"
"pid_oled\oled.o"
"pid_oled\oledfont.o"
"pid_oled\scheduler.o"
"pid_oled\oled_app.o"
"pid_oled\key_app.o"
--strict --scatter "pid_oled\pid_oled.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "pid_oled.map" -o pid_oled\pid_oled.axf