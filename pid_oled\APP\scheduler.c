#include "scheduler.h"

typedef struct
{
	void(*task_func)(void);
	uint32_t rate_ms;
	uint32_t last_run;
}task_t;

task_t task_list[] =
{
	{oled_task,10,0},
	{key_task,10,0},
};

uint8_t task_num;

void scheduler_init()
{
	task_num = sizeof(task_list) / sizeof(task_t);
}

void scheduler_run()
{
	for(uint8_t i = 0;i < task_num;i++)
	{
		uint32_t now_time = HAL_GetTick();
		if(now_time >= (task_list[i].rate_ms + task_list[i].last_run))
		{
			task_list[i].last_run = now_time;
			task_list[i].task_func();
		}
	}
}

